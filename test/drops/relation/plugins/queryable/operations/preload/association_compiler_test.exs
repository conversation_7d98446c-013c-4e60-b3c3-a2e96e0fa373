defmodule Drops.Relation.Plugins.Queryable.Operations.Preload.AssociationCompilerTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.Plugins.Queryable.Operations.Preload.AssociationCompiler
  import Ecto.Query

  describe "visit/2 with preload operations" do
    @tag relations: [:associations, :association_items, :association_parents]
    test "applies single association preload", %{associations: associations} do
      base_query = from(a in associations.__schema_module__())
      
      opts = %{
        opts: [preload: :parent],
        relation: associations.new()
      }
      
      result_query = AssociationCompiler.visit(base_query, opts)
      
      # Verify preload was applied
      assert %Ecto.Query{preloads: [parent: _]} = result_query
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "applies multiple association preloads", %{associations: associations} do
      base_query = from(a in associations.__schema_module__())
      
      opts = %{
        opts: [preload: [:parent, :items]],
        relation: associations.new()
      }
      
      result_query = AssociationCompiler.visit(base_query, opts)
      
      # Verify both preloads were applied
      assert %Ecto.Query{preloads: preloads} = result_query
      assert :parent in preloads
      assert :items in preloads
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "filters out invalid associations", %{associations: associations} do
      base_query = from(a in associations.__schema_module__())
      
      opts = %{
        opts: [preload: [:parent, :invalid_association, :items]],
        relation: associations.new()
      }
      
      result_query = AssociationCompiler.visit(base_query, opts)
      
      # Verify only valid preloads were applied
      assert %Ecto.Query{preloads: preloads} = result_query
      assert :parent in preloads
      assert :items in preloads
      refute :invalid_association in preloads
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "handles nested association preloads", %{associations: associations} do
      base_query = from(a in associations.__schema_module__())
      
      opts = %{
        opts: [preload: [parent: :associations]],
        relation: associations.new()
      }
      
      result_query = AssociationCompiler.visit(base_query, opts)
      
      # Verify nested preload was applied
      assert %Ecto.Query{preloads: [{:parent, :associations}]} = result_query
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "returns original query when no preload option", %{associations: associations} do
      base_query = from(a in associations.__schema_module__())
      
      opts = %{
        opts: [name: "test"],
        relation: associations.new()
      }
      
      result_query = AssociationCompiler.visit(base_query, opts)
      
      # Verify query is unchanged
      assert result_query == base_query
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "returns original query when preload is nil", %{associations: associations} do
      base_query = from(a in associations.__schema_module__())
      
      opts = %{
        opts: [preload: nil],
        relation: associations.new()
      }
      
      result_query = AssociationCompiler.visit(base_query, opts)
      
      # Verify query is unchanged
      assert result_query == base_query
    end
  end

  describe "association validation" do
    @tag relations: [:associations, :association_items, :association_parents]
    test "extracts available association names", %{associations: associations} do
      relation = associations.new()
      
      association_names = AssociationCompiler.visit(relation, %{extract: :association_names})
      
      # Verify expected associations are available
      assert :parent in association_names
      assert :items in association_names
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "validates single association", %{associations: associations} do
      relation = associations.new()
      
      result = AssociationCompiler.visit(:parent, %{relation: relation, filter: :valid_associations})
      assert result == :parent
      
      result = AssociationCompiler.visit(:invalid, %{relation: relation, filter: :valid_associations})
      assert result == nil
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "validates list of associations", %{associations: associations} do
      relation = associations.new()
      
      result = AssociationCompiler.visit(
        [:parent, :invalid, :items], 
        %{relation: relation, filter: :valid_associations}
      )
      
      assert result == [:parent, :items]
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "validates nested associations", %{associations: associations} do
      relation = associations.new()
      
      result = AssociationCompiler.visit(
        [parent: :associations, invalid: :something], 
        %{relation: relation, filter: :valid_associations}
      )
      
      assert result == [parent: :associations]
    end
  end
end
