defmodule Drops.Relation.Plugins.ReadingPreloadTest do
  use Drops.RelationCase, async: false

  describe "preload operations" do
    @tag relations: [:associations, :association_items, :association_parents]
    test "preloads single association", %{associations: associations, association_parents: parents} do
      # Create test data
      {:ok, parent} = parents.insert(%{description: "Test Parent"})
      {:ok, association} = associations.insert(%{name: "Test Association", parent_id: parent.id})

      # Test preload with single association
      result = associations.preload(:parent) |> associations.all()
      
      assert length(result) == 1
      loaded_association = List.first(result)
      assert loaded_association.name == "Test Association"
      assert loaded_association.parent.description == "Test Parent"
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "preloads multiple associations", %{associations: associations, association_items: items, association_parents: parents} do
      # Create test data
      {:ok, parent} = parents.insert(%{description: "Test Parent"})
      {:ok, association} = associations.insert(%{name: "Test Association", parent_id: parent.id})
      {:ok, _item} = items.insert(%{title: "Test Item", association_id: association.id})

      # Test preload with multiple associations
      result = associations.preload([:parent, :items]) |> associations.all()
      
      assert length(result) == 1
      loaded_association = List.first(result)
      assert loaded_association.name == "Test Association"
      assert loaded_association.parent.description == "Test Parent"
      assert length(loaded_association.items) == 1
      assert List.first(loaded_association.items).title == "Test Item"
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "preload with relation instance", %{associations: associations, association_parents: parents} do
      # Create test data
      {:ok, parent} = parents.insert(%{description: "Test Parent"})
      {:ok, association} = associations.insert(%{name: "Test Association", parent_id: parent.id})

      # Test preload on existing relation
      relation = associations.new()
      result = associations.preload(relation, :parent) |> associations.all()
      
      assert length(result) == 1
      loaded_association = List.first(result)
      assert loaded_association.parent.description == "Test Parent"
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "ignores invalid associations", %{associations: associations, association_parents: parents} do
      # Create test data
      {:ok, parent} = parents.insert(%{description: "Test Parent"})
      {:ok, association} = associations.insert(%{name: "Test Association", parent_id: parent.id})

      # Test preload with invalid association - should not crash
      result = associations.preload([:parent, :invalid_association]) |> associations.all()
      
      assert length(result) == 1
      loaded_association = List.first(result)
      assert loaded_association.parent.description == "Test Parent"
      # Should not have invalid_association field
      refute Map.has_key?(loaded_association, :invalid_association)
    end

    @tag relations: [:associations, :association_items, :association_parents]
    test "combines preload with restrict", %{associations: associations, association_parents: parents} do
      # Create test data
      {:ok, parent1} = parents.insert(%{description: "Parent 1"})
      {:ok, parent2} = parents.insert(%{description: "Parent 2"})
      {:ok, _assoc1} = associations.insert(%{name: "Association 1", parent_id: parent1.id})
      {:ok, _assoc2} = associations.insert(%{name: "Association 2", parent_id: parent2.id})

      # Test combining preload with restrict
      result = associations
        .restrict(name: "Association 1")
        .preload(:parent)
        |> associations.all()
      
      assert length(result) == 1
      loaded_association = List.first(result)
      assert loaded_association.name == "Association 1"
      assert loaded_association.parent.description == "Parent 1"
    end
  end

  describe "nullable field handling in restrict" do
    @tag relations: [:users]
    test "handles nil values for nullable fields", %{users: users} do
      # Create test data with nil email (assuming email is nullable)
      {:ok, user1} = users.insert(%{name: "User 1", email: nil})
      {:ok, user2} = users.insert(%{name: "User 2", email: "<EMAIL>"})

      # Test restricting by nil value
      result = users.restrict(email: nil) |> users.all()
      
      assert length(result) == 1
      assert List.first(result).name == "User 1"
    end

    @tag relations: [:users]
    test "handles non-nil values normally", %{users: users} do
      # Create test data
      {:ok, user1} = users.insert(%{name: "User 1", email: "<EMAIL>"})
      {:ok, user2} = users.insert(%{name: "User 2", email: "<EMAIL>"})

      # Test restricting by non-nil value
      result = users.restrict(email: "<EMAIL>") |> users.all()
      
      assert length(result) == 1
      assert List.first(result).name == "User 1"
    end

    @tag relations: [:users]
    test "handles list values with in clause", %{users: users} do
      # Create test data
      {:ok, user1} = users.insert(%{name: "User 1", email: "<EMAIL>"})
      {:ok, user2} = users.insert(%{name: "User 2", email: "<EMAIL>"})
      {:ok, user3} = users.insert(%{name: "User 3", email: "<EMAIL>"})

      # Test restricting by list of values
      result = users.restrict(email: ["<EMAIL>", "<EMAIL>"]) |> users.all()
      
      assert length(result) == 2
      names = Enum.map(result, & &1.name) |> Enum.sort()
      assert names == ["User 1", "User 2"]
    end
  end
end
