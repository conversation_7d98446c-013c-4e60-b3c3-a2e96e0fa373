defmodule Drops.Relation.Plugins.Queryable.Operations.Preload.AssociationCompiler do
  @moduledoc """
  Compiler for processing preload operations from opts in Queryable.

  This module validates preload associations against the schema module's available
  associations and reduces them to a valid query with preloads set.

  ## Examples

      # Simple association preload
      opts = [preload: :user]
      query = AssociationCompiler.visit(base_query, %{opts: opts, relation: relation})

      # Multiple associations
      opts = [preload: [:user, :comments]]
      query = AssociationCompiler.visit(base_query, %{opts: opts, relation: relation})

      # Nested associations
      opts = [preload: [user: :profile]]
      query = AssociationCompiler.visit(base_query, %{opts: opts, relation: relation})
  """

  import Ecto.Query

  @doc """
  Processes preload options and applies them to the query.

  Validates that all requested associations exist in the schema module
  and applies valid preloads to the query.

  ## Parameters

  - `queryable` - The base Ecto query to add preloads to
  - `opts` - Map containing `:opts` (keyword list with preload options) and `:relation` (relation struct)

  ## Returns

  Returns the query with valid preloads applied.

  ## Examples

      relation = %MyRelation{schema: schema, ...}
      opts = %{opts: [preload: :user], relation: relation}
      query = AssociationCompiler.visit(base_query, opts)
  """
  @spec visit(Ecto.Queryable.t(), map()) :: Ecto.Query.t()
  def visit(queryable, %{opts: opts, relation: relation}) when is_list(opts) do
    preload_value = Keyword.get(opts, :preload)

    if preload_value do
      valid_preloads = visit(preload_value, %{relation: relation, filter: :valid_associations})
      apply_preloads(queryable, valid_preloads)
    else
      queryable
    end
  end

  @doc """
  Validates preload associations against available schema associations.

  ## Parameters

  - `preload_value` - The preload specification (atom, list, or nested structure)
  - `opts` - Map containing `:relation` and `:filter` (:valid_associations)

  ## Returns

  Returns filtered preload specification containing only valid associations.
  """
  def visit(preload_value, %{relation: relation, filter: :valid_associations}) do
    available_associations = visit(relation, %{extract: :association_names})

    case preload_value do
      association when is_atom(association) ->
        if association in available_associations, do: association, else: nil

      associations when is_list(associations) ->
        filter_valid_associations(associations, available_associations)

      _ ->
        nil
    end
  end

  def visit(relation, %{extract: :association_names}) do
    relation_module = relation.__struct__

    if function_exported?(relation_module, :__schema_module__, 0) do
      schema_module = relation_module.__schema_module__()

      if function_exported?(schema_module, :__schema__, 1) do
        schema_module.__schema__(:associations)
      else
        []
      end
    else
      []
    end
  end

  # Private functions

  defp filter_valid_associations(associations, available_associations) do
    Enum.filter(associations, fn
      association when is_atom(association) ->
        association in available_associations

      {association, nested} when is_atom(association) ->
        # For nested associations like [user: :profile], validate the root association
        # Note: We don't validate nested associations here as they depend on the target schema
        association in available_associations

      _ ->
        false
    end)
  end

  defp apply_preloads(queryable, nil), do: queryable
  defp apply_preloads(queryable, []), do: queryable

  defp apply_preloads(queryable, preloads) when is_atom(preloads) do
    from(q in queryable, preload: ^preloads)
  end

  defp apply_preloads(queryable, preloads) when is_list(preloads) do
    from(q in queryable, preload: ^preloads)
  end
end
